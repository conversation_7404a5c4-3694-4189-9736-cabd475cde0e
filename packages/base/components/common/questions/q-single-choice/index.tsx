import { computed, defineComponent } from 'vue'
import type { PropType } from 'vue'
import { useQuestionsForm } from '@sa/hooks'
import QuestionStem from '../question-stem'
import styles from './index.module.css'

export default defineComponent({
  name: 'SingleChoice',
  props: {
    item: {
      type: Object as PropType<Question.ProcessedQuestionData>,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    disabled: {
      type: Boolean,
      default: undefined,
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },
    questionNumber: {
      type: Number,
      default: 1,
    },
    showQuestionStem: {
      type: Boolean,
      default: true,
    },
    stemVariant: {
      type: String as PropType<'default' | 'compact' | 'card'>,
      default: 'default',
    },
  },
  emits: ['update:modelValue', 'image-click'],
  setup(props, { emit }) {
    const modelValue = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })
    const isdisabled = computed(() => props.type === 'answer')
    const { mergedDisabled } = useQuestionsForm({
      type: computed(() => props.type),
    })

    const handleChange = (value: any) => {
      if (mergedDisabled.value)
        return
      modelValue.value = value
    }

    const handleImageClick = (imageSrc: string) => {
      emit('image-click', imageSrc)
    }

    return () => {
      return (
        <div>
          {/* 题干部分 */}
          {props.showQuestionStem && (
            <QuestionStem
              item={props.item}
              questionNumber={props.questionNumber}
              variant={props.stemVariant}
              onImage-click={handleImageClick}
            />
          )}

          {/* 选项部分 */}
          <ul class={styles.singleChoice}>
            {props.item.options?.map(option => (
              <li key={option.value}>
                <label class={styles.choiceItem}>
                  <div
                    class={[
                      styles.choiceItemQn,
                      !isdisabled && styles.cursor,
                      !isdisabled && props.item.correctAnswer === option.value ? styles.choiceItemQnChecked : '',
                    ]}
                    onClick={() => {
                      if (mergedDisabled.value) {
                        return
                      }
                      handleChange(option.value)
                    }}
                  >
                    <span>{option.value}</span>
                  </div>
                  <span
                    class={[styles.choiceItemLabel, 'contents']}
                    v-html={option.label}
                    v-katex
                  />
                </label>
              </li>
            ))}
          </ul>
        </div>
      )
    }
  },
})
