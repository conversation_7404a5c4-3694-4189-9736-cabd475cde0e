import { computed, defineComponent } from 'vue'
import type { PropType } from 'vue'
import { useQuestionsForm } from '@sa/hooks'
import styles from './index.module.css'

export default defineComponent({
  name: 'SingleChoice',
  props: {
    item: {
      type: Object as PropType<Question.ProcessedQuestionData>,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    disabled: {
      type: Boolean,
      default: undefined,
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },
  },
  emits: ['update:modelValue', 'image-click'],
  setup(props, { emit }) {
    const modelValue = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })
    const isdisabled = computed(() => props.type === 'answer')
    const { mergedDisabled } = useQuestionsForm({
      type: computed(() => props.type),
    })

    const handleChange = (value: any) => {
      if (mergedDisabled.value)
        return
      modelValue.value = value
    }

    return () => {
      return (
        <div>
          <ul class={styles.singleChoice}>
            {props.item.options?.map(option => (
              <li key={option.value}>
                <label class={styles.choiceItem}>
                  <div
                    class={[
                      styles.choiceItemQn,
                      !isdisabled && styles.cursor,
                      !isdisabled && props.item.correctAnswer === option.value ? styles.choiceItemQnChecked : '',
                    ]}
                    onClick={() => {
                      if (mergedDisabled.value) {
                        return
                      }
                      handleChange(option.value)
                    }}
                  >
                    <span>{option.value}</span>
                  </div>
                  <span
                    class={[styles.choiceItemLabel, 'contents']}
                    v-html={option.label}
                    v-katex
                  />
                </label>
              </li>
            ))}
          </ul>
        </div>

      )
    }
  },
})
