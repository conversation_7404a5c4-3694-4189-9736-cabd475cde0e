.singleChoice {

  .choiceItem {
    display: flex;
    align-items: center;
    min-height: 44px;
    margin-bottom: 12px;

    .choiceItemLabel {
      display: inline-flex;
    }

    .choiceItemQn {
      width: 30px;
      height: 30px;
      text-align: center;
      margin-right: 12px;
      font-weight: 500;
      color: #A2A2A2;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }
    .cursor{
      cursor: pointer;
      border-radius: 100%;
      background: #FFFFFF;
      border: 1px solid #A3A3A3;
    }

    .choiceItemQnChecked {
      background-color: #73CFFB;
      color: #FFFFFF;
      border: 1px solid #73CFFB;
    }
  }

}

