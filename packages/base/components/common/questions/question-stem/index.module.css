/* 题干组件基础样式 */
.questionStem {
  margin-bottom: 16px;
}

/* 题目头部信息 */
.questionHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

/* 题目序号 */
.questionNumber {
  font-size: 14px;
  font-weight: 500;
  color: #666666;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  white-space: nowrap;
}

/* 题型标签 */
.questionType {
  font-size: 12px;
  font-weight: 500;
  color: #1890ff;
  background-color: #e6f7ff;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #91d5ff;
  white-space: nowrap;
}

/* 题目内容 */
.questionContent {
  font-size: 16px;
  line-height: 1.6;
  color: #333333;
  word-wrap: break-word;
  word-break: break-word;
}

/* 题目内容中的图片样式 */
.questionContent img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.questionContent img:hover {
  transform: scale(1.02);
}

/* 题目内容中的数学公式样式 */
.questionContent .katex {
  font-size: inherit;
}

.questionContent .katex-display {
  margin: 12px 0;
}

/* 默认样式变体 */
.questionStem--default {
  /* 默认样式已在基础样式中定义 */
}

/* 紧凑样式变体 */
.questionStem--compact {
  margin-bottom: 12px;
}

.questionStem--compact .questionHeader {
  margin-bottom: 8px;
}

.questionStem--compact .questionNumber {
  font-size: 12px;
  padding: 2px 6px;
}

.questionStem--compact .questionType {
  font-size: 11px;
  padding: 1px 6px;
}

.questionStem--compact .questionContent {
  font-size: 14px;
  line-height: 1.5;
}

/* 卡片样式变体 */
.questionStem--card {
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.questionStem--card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.2s ease;
}

.questionStem--card .questionHeader {
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .questionContent {
    font-size: 15px;
  }
  
  .questionStem--compact .questionContent {
    font-size: 13px;
  }
  
  .questionHeader {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .questionNumber,
  .questionType {
    font-size: 12px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .questionStem--card {
    background-color: #1f1f1f;
    border-color: #333333;
  }
  
  .questionContent {
    color: #e8e8e8;
  }
  
  .questionNumber {
    background-color: #333333;
    color: #cccccc;
  }
  
  .questionType {
    background-color: #1a3a52;
    border-color: #2a5a82;
    color: #73cffb;
  }
}
